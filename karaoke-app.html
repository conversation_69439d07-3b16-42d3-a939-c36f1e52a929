<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> Duo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 300;
            color: #1d1d1f;
            margin-bottom: 30px;
        }

        .players {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 20px;
        }

        .player {
            flex: 1;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .player.active {
            border-color: #007aff;
            background: #e3f2fd;
            transform: scale(1.02);
        }

        .player h3 {
            font-size: 1.2rem;
            color: #1d1d1f;
            margin-bottom: 10px;
        }

        .score {
            font-size: 2rem;
            font-weight: 600;
            color: #007aff;
        }

        .controls {
            margin-bottom: 30px;
        }

        .control-info {
            background: #f0f0f0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            color: #666;
        }

        .lyrics-container {
            background: #1d1d1f;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lyrics {
            font-size: 1.5rem;
            line-height: 1.8;
            color: #fff;
            text-align: center;
        }

        .lyric-line {
            display: block;
            margin: 10px 0;
            opacity: 0.5;
            transition: all 0.5s ease;
        }

        .lyric-line.current {
            opacity: 1;
            color: #007aff;
            font-weight: 600;
            transform: scale(1.1);
        }

        .lyric-line.past {
            opacity: 0.3;
        }

        .button {
            background: #007aff;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            background: linear-gradient(90deg, #007aff, #00d4ff);
            border-radius: 3px;
            transition: width 0.1s ease;
            width: 0%;
        }

        .final-score {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
            display: none;
        }

        .final-score.show {
            display: block;
        }

        .winner {
            font-size: 1.5rem;
            color: #007aff;
            font-weight: 600;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Karaokê Duo</h1>
        
        <div class="players">
            <div class="player" id="player1">
                <h3>Jogador 1</h3>
                <div class="score" id="score1">0</div>
                <small>Tecla: A</small>
            </div>
            <div class="player" id="player2">
                <h3>Jogador 2</h3>
                <div class="score" id="score2">0</div>
                <small>Tecla: L</small>
            </div>
        </div>

        <div class="controls">
            <div class="control-info">
                Pressione as teclas A (Jogador 1) ou L (Jogador 2) no tempo certo da música!
            </div>
            <button class="button" id="startBtn" onclick="startGame()">Iniciar Karaokê</button>
            <button class="button" id="resetBtn" onclick="resetGame()" style="display: none;">Jogar Novamente</button>
        </div>

        <div class="progress-bar">
            <div class="progress" id="progress"></div>
        </div>

        <div class="lyrics-container">
            <div class="lyrics" id="lyrics">
                Pressione "Iniciar Karaokê" para começar!
            </div>
        </div>

        <div class="final-score" id="finalScore">
            <div class="winner" id="winner"></div>
            <p>Pontuação Final:</p>
            <p>Jogador 1: <span id="finalScore1">0</span> pontos</p>
            <p>Jogador 2: <span id="finalScore2">0</span> pontos</p>
        </div>
    </div>

    <script>
        // Música e letra sincronizada (tempos em segundos)
        const songData = {
            title: "Twinkle Twinkle Little Star",
            duration: 24,
            lyrics: [
                { time: 0, text: "Twinkle, twinkle, little star" },
                { time: 3, text: "How I wonder what you are" },
                { time: 6, text: "Up above the world so high" },
                { time: 9, text: "Like a diamond in the sky" },
                { time: 12, text: "Twinkle, twinkle, little star" },
                { time: 15, text: "How I wonder what you are" },
                { time: 18, text: "When the blazing sun is gone" },
                { time: 21, text: "When he nothing shines upon" }
            ]
        };

        let gameState = {
            isPlaying: false,
            startTime: 0,
            currentTime: 0,
            currentLyricIndex: 0,
            player1Score: 0,
            player2Score: 0,
            player1Hits: 0,
            player2Hits: 0,
            totalBeats: 0
        };

        let gameInterval;

        function startGame() {
            gameState.isPlaying = true;
            gameState.startTime = Date.now();
            gameState.currentTime = 0;
            gameState.currentLyricIndex = 0;
            gameState.player1Score = 0;
            gameState.player2Score = 0;
            gameState.player1Hits = 0;
            gameState.player2Hits = 0;
            gameState.totalBeats = 0;

            document.getElementById('startBtn').style.display = 'none';
            document.getElementById('resetBtn').style.display = 'none';
            document.getElementById('finalScore').classList.remove('show');

            updateLyrics();
            gameInterval = setInterval(updateGame, 100);

            // Simular batidas da música (a cada 0.5 segundos)
            setInterval(() => {
                if (gameState.isPlaying) {
                    gameState.totalBeats++;
                }
            }, 500);
        }

        function updateGame() {
            gameState.currentTime = (Date.now() - gameState.startTime) / 1000;
            
            // Atualizar barra de progresso
            const progress = (gameState.currentTime / songData.duration) * 100;
            document.getElementById('progress').style.width = Math.min(progress, 100) + '%';

            // Atualizar letra
            updateLyrics();

            // Verificar fim da música
            if (gameState.currentTime >= songData.duration) {
                endGame();
            }
        }

        function updateLyrics() {
            const lyricsContainer = document.getElementById('lyrics');
            
            // Encontrar a linha atual
            let currentIndex = -1;
            for (let i = 0; i < songData.lyrics.length; i++) {
                if (gameState.currentTime >= songData.lyrics[i].time) {
                    currentIndex = i;
                }
            }

            // Mostrar 3 linhas: anterior, atual, próxima
            let html = '';
            for (let i = Math.max(0, currentIndex - 1); i <= Math.min(songData.lyrics.length - 1, currentIndex + 2); i++) {
                let className = '';
                if (i < currentIndex) className = 'past';
                else if (i === currentIndex) className = 'current';
                
                html += `<span class="lyric-line ${className}">${songData.lyrics[i].text}</span>`;
            }

            lyricsContainer.innerHTML = html || 'Preparando...';
        }

        function handleKeyPress(player) {
            if (!gameState.isPlaying) return;

            // Verificar se está no tempo certo (margem de 0.3 segundos)
            const currentLyric = songData.lyrics[gameState.currentLyricIndex];
            if (currentLyric && Math.abs(gameState.currentTime - currentLyric.time) < 0.8) {
                if (player === 1) {
                    gameState.player1Hits++;
                    gameState.player1Score += 10;
                    document.getElementById('player1').classList.add('active');
                    setTimeout(() => document.getElementById('player1').classList.remove('active'), 200);
                } else {
                    gameState.player2Hits++;
                    gameState.player2Score += 10;
                    document.getElementById('player2').classList.add('active');
                    setTimeout(() => document.getElementById('player2').classList.remove('active'), 200);
                }
            }

            updateScores();
        }

        function updateScores() {
            document.getElementById('score1').textContent = gameState.player1Score;
            document.getElementById('score2').textContent = gameState.player2Score;
        }

        function endGame() {
            gameState.isPlaying = false;
            clearInterval(gameInterval);

            // Calcular pontuação final (0-100)
            const maxPossibleScore = songData.lyrics.length * 10;
            const finalScore1 = Math.round((gameState.player1Score / maxPossibleScore) * 100);
            const finalScore2 = Math.round((gameState.player2Score / maxPossibleScore) * 100);

            document.getElementById('finalScore1').textContent = finalScore1;
            document.getElementById('finalScore2').textContent = finalScore2;

            let winner = '';
            if (finalScore1 > finalScore2) {
                winner = '🏆 Jogador 1 Venceu!';
            } else if (finalScore2 > finalScore1) {
                winner = '🏆 Jogador 2 Venceu!';
            } else {
                winner = '🤝 Empate!';
            }

            document.getElementById('winner').textContent = winner;
            document.getElementById('finalScore').classList.add('show');
            document.getElementById('resetBtn').style.display = 'inline-block';
        }

        function resetGame() {
            gameState = {
                isPlaying: false,
                startTime: 0,
                currentTime: 0,
                currentLyricIndex: 0,
                player1Score: 0,
                player2Score: 0,
                player1Hits: 0,
                player2Hits: 0,
                totalBeats: 0
            };

            document.getElementById('startBtn').style.display = 'inline-block';
            document.getElementById('resetBtn').style.display = 'none';
            document.getElementById('finalScore').classList.remove('show');
            document.getElementById('progress').style.width = '0%';
            document.getElementById('lyrics').innerHTML = 'Pressione "Iniciar Karaokê" para começar!';
            updateScores();
        }

        // Event listeners para as teclas
        document.addEventListener('keydown', (event) => {
            if (event.key.toLowerCase() === 'a') {
                handleKeyPress(1);
            } else if (event.key.toLowerCase() === 'l') {
                handleKeyPress(2);
            }
        });

        // Inicializar scores
        updateScores();
    </script>
</body>
</html>
